# 🔐 Admin & Staff Panel Access Guide

## **Security Overview**
Your car wash application has enterprise-level security with:
- ✅ Role-based access control (RBAC)
- ✅ Rate limiting & account lockouts
- ✅ Two-factor authentication (2FA)
- ✅ IP whitelisting capability
- ✅ Comprehensive audit logging
- ✅ Session management

---

## **Access URLs**

### **Admin Panel**
```
https://your-app.vercel.app/admin/login
```
**Features:**
- Complete system control
- User management
- Booking oversight
- Financial reports
- System settings
- Staff management

### **Staff Portal**
```
https://your-app.vercel.app/staff/dashboard
```
**Features:**
- View assigned bookings
- Update service progress
- Upload progress photos
- Mark services complete
- Customer communication

---

## **User Roles & Permissions**

### **SUPER_ADMIN**
- 🔹 Full system access
- 🔹 Create/delete admin users
- 🔹 System configuration
- 🔹 Financial reports
- 🔹 Audit log access

### **ADMIN**
- 🔹 User management
- 🔹 Booking management
- 🔹 Staff oversight
- 🔹 Service tracking
- 🔹 Customer support

### **STAFF**
- 🔹 View assigned bookings
- 🔹 Update service status
- 🔹 Upload progress photos
- 🔹 Mark services complete
- 🔹 Limited customer info

---

## **How to Access Admin Panel**

### **Step 1: Create Admin User**
You need to create the first admin user in your database. Connect to your Neon database and run:

```sql
-- Create first admin user
INSERT INTO "AdminUser" (
  "id", "username", "email", "name", "password", "role", 
  "isActive", "twoFactorEnabled", "createdAt", "updatedAt"
) VALUES (
  gen_random_uuid(),
  'admin',
  '<EMAIL>',
  'System Administrator',
  '$2b$12$LQv3c1yqBwEHxE2fsuEH0uySc4p6ERxF7HhJSQC8ygc4' -- password: 'carwash2024'
  'SUPER_ADMIN',
  true,
  false,
  NOW(),
  NOW()
);
```

### **Step 2: Access Admin Panel**
1. Go to: `https://your-app.vercel.app/admin/login`
2. Login with:
   - **Username**: `admin`
   - **Password**: `carwash2024`

### **Step 3: Secure Your Admin Account**
1. **Change default password** immediately
2. **Enable 2FA** for added security
3. **Set up IP whitelisting** (optional)

---

## **Service Progress Tracking**

### **For Staff Users:**
1. **Login** to staff portal
2. **View assigned bookings**
3. **Update service status:**
   - `IN_PROGRESS` - Service started
   - `COMPLETED` - Service finished
4. **Upload progress photos:**
   - Before/during/after shots
   - Damage documentation
   - Quality checks

### **Customer Visibility:**
- Customers receive **real-time updates**
- **Photo notifications** when images uploaded
- **Progress tracking** on booking page
- **Estimated completion** time updates

---

## **Database Setup Commands**

Run these SQL commands in your Neon database to set up admin access:

```sql
-- 1. Create Admin User
INSERT INTO "AdminUser" (
  "id", "username", "email", "name", "password", "role", 
  "isActive", "createdAt", "updatedAt"
) VALUES (
  gen_random_uuid(),
  'admin',
  '<EMAIL>', 
  'System Administrator',
  '$2b$12$LQv3c1yqBwEHxE2fsuEH0uOE6UdmOhHhWqBv.r7vEhE6mNh.K',
  'SUPER_ADMIN',
  true,
  NOW(),
  NOW()
);

-- 2. Create Staff User  
INSERT INTO "AdminUser" (
  "id", "username", "email", "name", "password", "role",
  "isActive", "createdAt", "updatedAt"
) VALUES (
  gen_random_uuid(),
  'staff1',
  '<EMAIL>',
  'Car Wash Staff',
  '$2b$12$LQv3c1yqBwEHxE2fsuEH0uOE6UdmOhHhWqBv.r7vEhE6mNh.K',
  'STAFF', 
  true,
  NOW(),
  NOW()
);
```

**Default Credentials:**
- **Username**: `admin` / Password: `carwash2024`
- **Username**: `staff1` / Password: `carwash2024`

⚠️ **IMPORTANT**: Change these passwords immediately after first login!

---

## **Security Best Practices**

### **Admin Security:**
1. ✅ Use strong, unique passwords
2. ✅ Enable 2FA immediately
3. ✅ Whitelist admin IP addresses
4. ✅ Regular password changes
5. ✅ Monitor audit logs

### **Staff Security:**
1. ✅ Individual staff accounts
2. ✅ Regular access reviews
3. ✅ Training on data sensitivity
4. ✅ Limited data access
5. ✅ Activity monitoring

### **Data Protection:**
1. ✅ Customer data is encrypted
2. ✅ Payment info secured
3. ✅ Regular backups
4. ✅ GDPR compliance
5. ✅ Audit trail maintained

---

## **Emergency Access**

If locked out of admin panel:
1. **Check rate limiting** (15 min lockout)
2. **Database direct access** via Neon
3. **Reset password** using SQL
4. **Contact system administrator**

---

## **Next Steps After Deployment**

1. **Create admin users** using SQL commands above
2. **Test admin login** at `/admin/login` 
3. **Set up staff accounts**
4. **Configure 2FA** for security
5. **Train staff** on progress tracking
6. **Test customer notifications**

Your admin panel is enterprise-grade and ready for production use! 🚀