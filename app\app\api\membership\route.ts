import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';

export const dynamic = "force-dynamic";

// GET - Fetch membership plans and user membership status
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get membership plans (simplified to Basic and Premium)
    const membershipPlans = [
      {
        id: 'BASIC',
        name: 'Basic Member',
        description: 'Perfect for occasional car washes',
        price: 4900, // R49 per month in cents
        features: [
          '10% discount on all services',
          'Standard booking priority',
          '1x loyalty points',
          'Monthly newsletter',
          'Email customer support'
        ],
        popular: false
      },
      {
        id: 'PREMIUM',
        name: 'Premium Member',
        description: 'Great value for regular car care',
        price: 9900, // R99 per month in cents
        features: [
          '20% discount on all services',
          'Priority booking slots',
          '2x loyalty points earned',
          'Free tire shine monthly',
          'WhatsApp customer support',
          'Birthday month special discount',
          'Booking reminders via SMS'
        ],
        popular: true
      }
    ];

    // Get user's current membership
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      include: { membership: true }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    return NextResponse.json({
      plans: membershipPlans,
      currentMembership: user.membership ? {
        plan: user.membership.plan,
        isActive: user.membership.isActive,
        startDate: user.membership.startDate,
        endDate: user.membership.endDate,
        autoRenew: user.membership.autoRenew,
        price: user.membership.price
      } : null,
      isAdmin: user.email === '<EMAIL>'
    });

  } catch (error) {
    console.error('💥 Membership API error:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}

// POST - Subscribe to a membership plan
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { planId } = await request.json();

    if (!['BASIC', 'PREMIUM'].includes(planId)) {
      return NextResponse.json({ error: 'Invalid membership plan' }, { status: 400 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      include: { membership: true }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Admin gets free membership
    const isAdmin = user.email === '<EMAIL>';
    
    const planPrices = {
      BASIC: 4900,
      PREMIUM: 9900
    };

    // For non-admin users, provide payment options
    if (!isAdmin) {
      // Return payment options instead of immediately requiring online payment
      const payFastData = {
        merchant_id: process.env.PAYFAST_MERCHANT_ID,
        merchant_key: process.env.PAYFAST_MERCHANT_KEY,
        return_url: `${process.env.NEXTAUTH_URL}/membership/success`,
        cancel_url: `${process.env.NEXTAUTH_URL}/membership/cancel`,
        notify_url: `${process.env.NEXTAUTH_URL}/api/membership/payfast-webhook`,
        name_first: user.firstName || 'Customer',
        name_last: user.lastName || '',
        email_address: user.email,
        amount: (planPrices[planId as keyof typeof planPrices] / 100).toFixed(2), // Convert cents to rands
        item_name: `${planId} Membership Subscription`,
        item_description: `Monthly ${planId.toLowerCase()} membership plan`,
        custom_str1: user.id, // User ID for tracking
        custom_str2: planId, // Plan ID for activation
        subscription_type: '1', // Recurring subscription
        recurring_amount: (planPrices[planId as keyof typeof planPrices] / 100).toFixed(2),
        frequency: '3', // Monthly
        cycles: '0' // Indefinite until cancelled
      };

      return NextResponse.json({
        requiresPaymentSelection: true,
        payFastData,
        planId,
        amount: planPrices[planId as keyof typeof planPrices],
        message: 'Choose your payment method for membership activation'
      });
    }

    // If user already has a membership, update it
    if (user.membership) {
      const updatedMembership = await prisma.membership.update({
        where: { id: user.membership.id },
        data: {
          plan: planId,
          price: isAdmin ? 0 : planPrices[planId as keyof typeof planPrices],
          isActive: true,
          startDate: new Date(),
          endDate: isAdmin ? null : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
          autoRenew: !isAdmin
        }
      });

      return NextResponse.json({ 
        success: true, 
        membership: updatedMembership,
        message: isAdmin ? 'Admin membership updated successfully!' : 'Membership upgraded successfully!'
      });
    } else {
      // Create new membership
      const newMembership = await prisma.membership.create({
        data: {
          userId: user.id,
          plan: planId,
          price: isAdmin ? 0 : planPrices[planId as keyof typeof planPrices],
          startDate: new Date(),
          endDate: isAdmin ? null : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
          isActive: true,
          autoRenew: !isAdmin
        }
      });

      return NextResponse.json({ 
        success: true, 
        membership: newMembership,
        message: isAdmin ? 'Admin membership activated successfully!' : 'Welcome to your new membership!'
      });
    }

  } catch (error) {
    console.error('💥 Membership subscription error:', error);
    return NextResponse.json({ 
      error: 'Failed to process membership subscription' 
    }, { status: 500 });
  }
}

// DELETE - Cancel membership
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      include: { membership: true }
    });

    if (!user || !user.membership) {
      return NextResponse.json({ error: 'No active membership found' }, { status: 404 });
    }

    // Admin memberships cannot be cancelled this way
    const isAdmin = user.email === '<EMAIL>';
    if (isAdmin) {
      return NextResponse.json({ 
        error: 'Admin membership cannot be cancelled' 
      }, { status: 403 });
    }

    await prisma.membership.update({
      where: { id: user.membership.id },
      data: {
        isActive: false,
        autoRenew: false,
        endDate: new Date() // End immediately
      }
    });

    return NextResponse.json({ 
      success: true, 
      message: 'Membership cancelled successfully' 
    });

  } catch (error) {
    console.error('💥 Membership cancellation error:', error);
    return NextResponse.json({ 
      error: 'Failed to cancel membership' 
    }, { status: 500 });
  }
}