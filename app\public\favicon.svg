<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle with gradient -->
  <circle cx="16" cy="16" r="16" fill="url(#gradient)"/>
  
  <!-- Car silhouette -->
  <path d="M8 18h2l1-2h10l1 2h2c1.1 0 2 .9 2 2v4c0 1.1-.9 2-2 2h-1v1c0 .6-.4 1-1 1s-1-.4-1-1v-1H11v1c0 .6-.4 1-1 1s-1-.4-1-1v-1H8c-1.1 0-2-.9-2-2v-4c0-1.1.9-2 2-2z" fill="white" opacity="0.9"/>
  <path d="M10.5 16.5h11l-1-2h-9l-1 2z" fill="white" opacity="0.7"/>
  
  <!-- Wheels -->
  <circle cx="11" cy="21" r="1.5" fill="white" opacity="0.8"/>
  <circle cx="21" cy="21" r="1.5" fill="white" opacity="0.8"/>
  
  <!-- Water drops/bubbles -->
  <circle cx="8" cy="8" r="1" fill="white" opacity="0.6"/>
  <circle cx="24" cy="10" r="0.8" fill="white" opacity="0.5"/>
  <circle cx="6" cy="12" r="0.6" fill="white" opacity="0.4"/>
  
  <!-- Gradient definition -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2563eb;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
