@tailwind base;
@tailwind components;
@tailwind utilities;
 
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
 
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
 
    --primary: 0 73% 41%;
    --primary-foreground: 210 40% 98%;
    
    --secondary: 224 64% 33%;
    --secondary-foreground: 210 40% 98%;
 
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
 
    --accent: 224 64% 33%;
    --accent-foreground: 210 40% 98%;
 
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 0 73% 41%;
 
    --radius: 0.5rem;
    
    /* Ekhaya Brand Colors */
    --ekhaya-red: 0 73% 41%;
    --ekhaya-red-light: 0 86% 97%;
    --ekhaya-red-dark: 0 75% 30%;
    --ekhaya-blue: 224 64% 33%;
    --ekhaya-blue-light: 221 100% 97%;
    --ekhaya-blue-dark: 224 70% 20%;
  }
 
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
 
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
 
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
 
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
 
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
 
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
 
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
 
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
 
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}
 
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Ekhaya Custom Styles */
.ekhaya-gradient {
  background: linear-gradient(135deg, hsl(var(--ekhaya-red)) 0%, hsl(var(--ekhaya-blue)) 100%);
}

.ekhaya-red {
  color: hsl(var(--ekhaya-red));
}

.ekhaya-blue {
  color: hsl(var(--ekhaya-blue));
}

.service-card {
  transition: all 0.3s ease;
  border: 2px solid transparent;
  background: white;
  border-radius: 1rem;
  overflow: hidden;
}

.service-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-color: hsl(var(--ekhaya-red));
}

.service-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

/* Logo Styles */
.logo-container {
  overflow: hidden;
  border-radius: 0.75rem;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.logo-animated {
  transition: all 0.3s ease;
  animation: logoEntrance 2s ease-out;
}

.logo-animated:hover {
  transform: scale(1.05);
  filter: brightness(1.1);
}

/* Car Motion Animation on Page Load */
@keyframes logoEntrance {
  0% {
    transform: translateX(-100px) scale(0.8);
    opacity: 0;
  }
  50% {
    transform: translateX(10px) scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
}

/* Car driving animation for hero section */
@keyframes carDriveIn {
  0% {
    transform: translateX(-200px) scale(0.8);
    opacity: 0;
  }
  25% {
    transform: translateX(-50px) scale(0.9);
    opacity: 0.6;
  }
  50% {
    transform: translateX(20px) scale(1.1);
    opacity: 0.8;
  }
  75% {
    transform: translateX(-10px) scale(1.05);
    opacity: 0.9;
  }
  100% {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
}

.car-entrance {
  animation: carDriveIn 3s ease-out;
}

/* Logo pulse animation for brand recognition */
@keyframes logoPulse {
  0%, 100% {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 12px 40px rgba(201, 42, 42, 0.2);
  }
}

.logo-container:hover {
  animation: logoPulse 2s ease-in-out infinite;
}

/* Mobile responsive logo */
@media (max-width: 768px) {
  .logo-container {
    height: 3rem !important;
    width: 12rem !important;
  }
  
  .logo-animated {
    animation-duration: 1.5s;
  }
  
  @keyframes logoEntrance {
    0% {
      transform: translateX(-50px) scale(0.9);
      opacity: 0;
    }
    50% {
      transform: translateX(5px) scale(1.02);
      opacity: 0.8;
    }
    100% {
      transform: translateX(0) scale(1);
      opacity: 1;
    }
  }
}

/* Enhanced car trail effect */
.car-trail {
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255,255,255,0.1) 10%, 
    rgba(255,255,255,0.3) 50%, 
    rgba(255,255,255,0.1) 90%, 
    transparent 100%);
  height: 3px;
  border-radius: 2px;
  box-shadow: 0 0 10px rgba(255,255,255,0.2);
}

/* Smooth page load animation */
.page-entrance {
  animation: pageLoad 1s ease-out;
}

@keyframes pageLoad {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Logo shadow enhancement */
.logo-container {
  position: relative;
}

.logo-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(201, 42, 42, 0.1) 0%, rgba(37, 99, 235, 0.1) 100%);
  border-radius: 0.75rem;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.logo-container:hover::before {
  opacity: 1;
}

/* Carousel Styles */
.embla {
  overflow: hidden;
}

.embla__container {
  display: flex;
  transition: transform 0.3s ease-in-out;
}

.embla__slide {
  flex: 0 0 100%;
  min-width: 0;
}

/* Smooth carousel slide transition */
.carousel-slide {
  opacity: 1;
  transform: translateX(0);
  transition: all 0.5s ease-in-out;
}

.carousel-slide.entering {
  opacity: 0;
  transform: translateX(20px);
}

.carousel-slide.exiting {
  opacity: 0;
  transform: translateX(-20px);
}

/* Carousel dots hover effect */
.carousel-dot {
  transition: all 0.2s ease;
}

.carousel-dot:hover {
  scale: 1.2;
  opacity: 0.8;
}

.btn-ekhaya-red {
  background: hsl(var(--ekhaya-red));
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-ekhaya-red:hover {
  background: hsl(var(--ekhaya-red-dark));
  transform: translateY(-1px);
}

.btn-ekhaya-blue {
  background: hsl(var(--ekhaya-blue));
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-ekhaya-blue:hover {
  background: hsl(var(--ekhaya-blue-dark));
  transform: translateY(-1px);
}

/* Enhanced Professional Logo Animations */
@keyframes logoBlendFade {
  0% {
    opacity: 0;
    filter: blur(2px) brightness(0.8);
    transform: scale(0.95);
  }
  50% {
    opacity: 0.8;
    filter: blur(0.5px) brightness(1.05);
    transform: scale(1.02);
  }
  100% {
    opacity: 1;
    filter: blur(0) brightness(1);
    transform: scale(1);
  }
}

@keyframes logoFloating {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-3px) rotate(0.5deg);
  }
  50% {
    transform: translateY(-1px) rotate(0deg);
  }
  75% {
    transform: translateY(-2px) rotate(-0.5deg);
  }
}

@keyframes logoGlowPulse {
  0%, 100% {
    filter: drop-shadow(0 0 3px rgba(59, 130, 246, 0.2));
  }
  50% {
    filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.4)) drop-shadow(0 0 15px rgba(147, 51, 234, 0.2));
  }
}

@keyframes logoShimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
}

/* Professional logo container with blended edges */
.logo-professional {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  backdrop-filter: blur(12px);
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.15) 0%, 
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.1) 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  animation: logoBlendFade 1.5s ease-out;
}

.logo-professional::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, 
    transparent, 
    rgba(59, 130, 246, 0.1), 
    transparent, 
    rgba(147, 51, 234, 0.1), 
    transparent);
  border-radius: 18px;
  z-index: -1;
  background-size: 400px 400px;
  animation: logoShimmer 3s ease-in-out infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.logo-professional:hover::before {
  opacity: 1;
}

.logo-professional:hover {
  animation: logoFloating 4s ease-in-out infinite, logoGlowPulse 2s ease-in-out infinite;
  transform: scale(1.03);
  transition: transform 0.3s ease;
}

/* Auth page specific logo styling */
.logo-auth {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.25) 0%, 
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.15) 100%);
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.15),
    inset 0 2px 0 rgba(255, 255, 255, 0.3),
    inset 0 -2px 0 rgba(0, 0, 0, 0.1);
}

/* Footer logo styling */
.logo-footer {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.08) 0%, 
    rgba(255, 255, 255, 0.03) 50%,
    rgba(255, 255, 255, 0.06) 100%);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 
    0 8px 25px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* Image blending effects */
.logo-image-blend {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  filter: brightness(1.02) contrast(1.05) saturate(1.1);
  mix-blend-mode: multiply;
}

.logo-image-blend:hover {
  filter: brightness(1.1) contrast(1.1) saturate(1.2);
  transform: scale(1.01);
}

/* Soft edge blending overlay */
.logo-blend-overlay {
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at center, 
    transparent 40%, 
    rgba(255, 255, 255, 0.1) 70%, 
    rgba(255, 255, 255, 0.2) 100%);
  pointer-events: none;
  transition: opacity 0.3s ease;
  opacity: 0.3;
}

.logo-professional:hover .logo-blend-overlay {
  opacity: 0.6;
}

/* Responsive animations */
@media (max-width: 768px) {
  .logo-professional {
    animation-duration: 1s;
  }
  
  .logo-professional:hover {
    animation: none;
    transform: scale(1.02);
  }
  
  @keyframes logoBlendFade {
    0% {
      opacity: 0;
      transform: scale(0.98);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .logo-professional,
  .logo-professional:hover,
  .logo-professional::before,
  .business-name,
  .business-name:hover,
  .business-name::before,
  .business-name h1,
  .business-name p,
  .business-name h1 span,
  .business-name .ekhaya-red,
  .business-name .ekhaya-blue {
    animation: none !important;
    transform: none !important;
    transition: none !important;
  }

  .business-name:hover h1,
  .business-name:hover p {
    transform: none !important;
  }
}

/* SweetAlert2 Button Fixes */
.swal2-popup {
  font-family: inherit !important;
}

.swal2-actions {
  display: flex !important;
  justify-content: center !important;
  gap: 1rem !important;
  margin-top: 1.5rem !important;
}

.swal2-confirm,
.swal2-cancel {
  display: inline-block !important;
  padding: 0.5rem 1.5rem !important;
  border-radius: 0.375rem !important;
  font-weight: 500 !important;
  font-size: 0.875rem !important;
  border: none !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.swal2-confirm {
  background-color: #ef4444 !important;
  color: white !important;
}

.swal2-confirm:hover {
  background-color: #dc2626 !important;
}

.swal2-cancel {
  background-color: #6b7280 !important;
  color: white !important;
}

.swal2-cancel:hover {
  background-color: #4b5563 !important;
}

/* Business Name Styling with Mirror Effects and Animations */
.business-name {
  position: relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: businessNameEntrance 2s ease-out;
}

.business-name::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
  z-index: 1;
  pointer-events: none;
}

.business-name:hover::before {
  transform: translateX(100%);
}

.business-name:hover {
  transform: translateY(-2px) scale(1.02);
  text-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

/* Mirror reflection effect */
.business-name::after {
  content: attr(data-text);
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
  -webkit-mask: linear-gradient(to bottom, rgba(0, 0, 0, 0.3) 0%, transparent 100%);
  mask: linear-gradient(to bottom, rgba(0, 0, 0, 0.3) 0%, transparent 100%);
  transform: scaleY(-1);
  opacity: 0.4;
  pointer-events: none;
  z-index: -1;
}

.business-name .ekhaya-red {
  position: relative;
  background: linear-gradient(135deg,
    hsl(var(--ekhaya-red)) 0%,
    hsl(var(--ekhaya-red-light)) 30%,
    hsl(var(--ekhaya-red)) 60%,
    hsl(var(--ekhaya-red-dark)) 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
  animation: shimmer 3s ease-in-out infinite;
  filter: drop-shadow(0 0 10px rgba(220, 38, 38, 0.3));
}

.business-name .ekhaya-blue {
  position: relative;
  background: linear-gradient(135deg,
    hsl(var(--ekhaya-blue)) 0%,
    hsl(var(--ekhaya-blue-light)) 30%,
    hsl(var(--ekhaya-blue)) 60%,
    hsl(var(--ekhaya-blue-dark)) 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  letter-spacing: 0.1em;
  animation: shimmer 3s ease-in-out infinite 0.5s;
  filter: drop-shadow(0 0 8px rgba(37, 99, 235, 0.3));
}

/* Entrance Animation */
@keyframes businessNameEntrance {
  0% {
    opacity: 0;
    transform: translateY(-30px) scale(0.8);
    filter: blur(10px);
  }
  50% {
    opacity: 0.8;
    transform: translateY(5px) scale(1.05);
    filter: blur(2px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

/* Shimmer Animation */
@keyframes shimmer {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Floating Animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-3px);
  }
}

.business-name h1 {
  animation: float 4s ease-in-out infinite;
}

.business-name p {
  animation: float 4s ease-in-out infinite 0.5s;
}

/* Enhanced Glass Reflection Effect */
.business-name .glass-reflection {
  background: linear-gradient(
    105deg,
    transparent 40%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0.6) 52%,
    rgba(255, 255, 255, 0.4) 54%,
    transparent 60%
  );
}

/* Pulsing Glow Animation */
@keyframes pulseGlow {
  0%, 100% {
    box-shadow:
      0 0 20px rgba(220, 38, 38, 0.3),
      0 0 40px rgba(37, 99, 235, 0.2),
      0 0 60px rgba(220, 38, 38, 0.1);
  }
  50% {
    box-shadow:
      0 0 30px rgba(220, 38, 38, 0.5),
      0 0 60px rgba(37, 99, 235, 0.3),
      0 0 80px rgba(220, 38, 38, 0.2);
  }
}

.business-name:hover {
  animation: pulseGlow 2s ease-in-out infinite;
}

/* Text Reveal Animation */
@keyframes textReveal {
  0% {
    clip-path: inset(0 100% 0 0);
  }
  100% {
    clip-path: inset(0 0 0 0);
  }
}

.business-name h1 span {
  animation: textReveal 1.5s ease-out;
}

.business-name p {
  animation: textReveal 1.5s ease-out 0.3s both;
}

/* 3D Perspective Effect */
.business-name {
  perspective: 1000px;
  transform-style: preserve-3d;
}

.business-name:hover h1 {
  transform: rotateX(5deg) rotateY(-2deg) translateZ(10px);
}

.business-name:hover p {
  transform: rotateX(-3deg) rotateY(1deg) translateZ(5px);
}

/* Animation Delay Utilities */
.animation-delay-500 {
  animation-delay: 0.5s;
}

.animation-delay-1000 {
  animation-delay: 1s;
}

/* Enhanced Mirror Effect */
.business-name .mirror-reflection {
  background: linear-gradient(to bottom,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    transparent 100%);
  -webkit-mask-image: linear-gradient(to bottom,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0.1) 50%,
    transparent 100%);
  mask-image: linear-gradient(to bottom,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0.1) 50%,
    transparent 100%);
}

/* Sparkle Effect on Hover */
@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

.business-name:hover .animate-ping {
  animation: sparkle 2s ease-in-out infinite;
}